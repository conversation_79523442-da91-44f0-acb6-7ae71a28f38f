<script setup lang="ts">
import type { ScanResult } from '@/utils/wechat'
import { showFailToast, showSuccessToast } from 'vant'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { axiosIns } from '@/api/axios'
import { useWechatSDK } from '@/store/wechat'
import { isWechatBrowser, wechatSDK } from '@/utils/wechat'
import { checkScanPermission, fixScanPermission, showPermissionSuggestions } from '@/utils/wechatScanFix'

const props = withDefaults(defineProps<Props>(), {
  buttonText: '扫一扫',
  buttonType: 'primary',
  buttonSize: 'normal',
  buttonClass: '',
  disabled: false,
})

const router = useRouter()

interface Props {
  buttonText?: string
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  buttonSize?: 'large' | 'normal' | 'small' | 'mini'
  buttonClass?: string
  disabled?: boolean
  onSuccess?: (result: string, parsedResult?: any) => void
  onError?: (error: Error) => void
}

const scanning = ref(false)
const scanResult = ref('')
const parsedResult = ref<any>(null)
const showResult = ref(false)
const showLoginDialog = ref(false)
const loginData = ref<any>(null)
const loginLoading = ref(false)
const showDebugInfo = ref(false)
const fixingPermission = ref(false)

// 开发环境标识
const isDev = import.meta.env.DEV

// 当前页面URL
const currentUrl = computed(() => window.location.href)

// 使用微信SDK状态管理
const wechatStore = useWechatSDK()

// 检测是否在微信环境中
const isInWechat = computed(() => isWechatBrowser())

// 检测是否支持微信扫码API
const supportWxScan = computed(() => {
  return wechatStore.canScan
})

// 初始化微信SDK
async function initWechat() {
  try {
    await wechatStore.ensureInitialized()

    // 调试信息：输出微信SDK配置状态
    console.log('微信SDK初始化完成')
    console.log('当前URL:', window.location.href)
    console.log('微信环境检测:', isInWechat.value)
    console.log('支持扫码:', supportWxScan.value)
    console.log('SDK状态:', wechatSDK.getStatus())

    // 检查微信JS-SDK是否正确加载
    const wx = (window as any).wx
    if (wx) {
      console.log('微信JS-SDK已加载')
      console.log('可用的微信API:', Object.keys(wx).filter(key => typeof wx[key] === 'function'))
    }
    else {
      console.error('微信JS-SDK未加载')
    }
  }
  catch (error) {
    console.error('微信SDK初始化失败:', error)
  }
}

// 处理扫码
async function handleScan() {
  if (scanning.value || props.disabled)
    return

  try {
    scanning.value = true
    console.log('supportWxScan.value', supportWxScan.value)
    // 优先使用微信扫码API
    if (supportWxScan.value) {
      await scanWithWechat()
    }
    else {
      // 暂时使用模拟扫码，后续可以集成摄像头扫码
      // await simulateScan()
      // H5不支持扫码
      showFailToast('H5不支持扫码功能')
    }
  }
  catch (error) {
    console.error('扫码失败:', error)
    props.onError?.(error as Error)
  }
  finally {
    scanning.value = false
  }
}

// 微信扫码
async function scanWithWechat() {
  try {
    console.log('开始微信扫码...')

    // 1. 先检查扫码权限
    const permissionCheck = await checkScanPermission()
    if (!permissionCheck.canScan) {
      console.error('扫码权限检查失败:', permissionCheck)

      // 显示权限问题和建议
      showPermissionSuggestions(permissionCheck)

      // 尝试自动修复
      console.log('尝试自动修复扫码权限...')
      const fixResult = await fixScanPermission()

      if (!fixResult) {
        throw new Error(permissionCheck.error || '扫码权限不可用')
      }

      console.log('扫码权限修复成功，继续扫码...')
    }

    // 2. 执行扫码
    const result = await wechatSDK.scanQRCode({
      needResult: 1,
      scanType: ['qrCode', 'barCode'],
      success: (res: ScanResult) => {
        console.log('微信扫码成功:', res)
      },
      fail: (error: any) => {
        console.error('微信扫码失败:', error)
      },
    })

    console.log('扫码结果:', result)
    handleScanResult(result.resultStr)
    return result.resultStr
  }
  catch (error) {
    console.error('微信扫码异常:', error)

    // 提供更详细的错误信息和解决方案
    let errorMessage = '微信扫码失败'
    if (error instanceof Error) {
      if (error.message.includes('permission denied')) {
        errorMessage = '扫码权限被拒绝'
        showFailToast('扫码权限被拒绝，请检查微信公众号配置')
      }
      else if (error.message.includes('invalid signature')) {
        errorMessage = '微信签名验证失败'
        showFailToast('微信签名验证失败，请联系管理员')
      }
      else if (error.message.includes('invalid url domain')) {
        errorMessage = '当前域名未配置为微信安全域名'
        showFailToast('域名配置错误，请联系管理员')
      }
      else if (error.message.includes('cancel')) {
        errorMessage = '用户取消扫码'
        // 用户主动取消，不显示错误提示
        return
      }
      else {
        errorMessage = error.message
        showFailToast(errorMessage)
      }
    }

    throw new Error(errorMessage)
  }
}

// 处理扫码结果
function handleScanResult(result: string) {
  scanResult.value = result
  parsedResult.value = parseQrCodeData(result)

  // 根据扫码结果类型进行不同处理
  const resultType = parsedResult.value?.type

  switch (resultType) {
    case 'workstation_info':
      handleWorkstationInfo(parsedResult.value)
      break
    case 'login':
      handleLoginConfirm(parsedResult.value)
      break
    default:
      showResult.value = true
      break
  }

  props.onSuccess?.(result, parsedResult.value)
}

// 解析二维码数据
function parseQrCodeData(data: string) {
  try {
    // 尝试解析JSON格式的二维码
    const parsed = JSON.parse(data)
    return parsed
  }
  catch {
    // 如果不是JSON格式，检查是否是URL
    if (data.startsWith('http://') || data.startsWith('https://')) {
      return {
        type: 'url',
        url: data,
      }
    }
    // 其他格式的数据
    return {
      type: 'text',
      content: data,
    }
  }
}

// 处理工位信息
function handleWorkstationInfo(data: any) {
  // 跳转到工位信息页面，传递真实的工位数据
  const queryData = encodeURIComponent(JSON.stringify(data))
  router.push({
    path: '/workstation/info',
    query: {
      data: queryData,
      workstationId: data.workstationId,
      projectId: data.projectId,
    },
  })
}

// 处理登录确认
function handleLoginConfirm(data: any) {
  showLoginDialog.value = true
  loginData.value = data
}

// 调用登录确认API
async function confirmLoginApi(token: string) {
  const formData = new FormData()
  formData.append('token', token)

  return axiosIns.post('/v1/location/home/<USER>/confirm-login', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 处理登录确认
async function handleLoginConfirmAction() {
  if (loginLoading.value)
    return

  try {
    loginLoading.value = true
    console.log('确认登录:', loginData.value)

    if (!loginData.value?.token) {
      showFailToast('缺少登录token')
      return
    }

    // 调用登录确认API
    const response = await confirmLoginApi(loginData.value.token)
    console.log('登录成功:', response)

    showSuccessToast('登录成功')
    closeLoginDialog()
  }
  catch (error) {
    console.error('登录失败:', error)
    showFailToast('登录失败，请重试')
  }
  finally {
    loginLoading.value = false
  }
}

// 关闭登录弹窗
function closeLoginDialog() {
  showLoginDialog.value = false
  loginData.value = null
}

// 关闭结果弹窗
function handleResultConfirm() {
  showResult.value = false
  scanResult.value = ''
  parsedResult.value = null
}

// 调试微信SDK配置
async function debugWechatConfig() {
  console.log('=== 微信SDK调试信息 ===')

  // 基础环境检查
  console.log('1. 环境检查:')
  console.log('  - 当前URL:', window.location.href)
  console.log('  - User Agent:', navigator.userAgent)
  console.log('  - 是否微信环境:', isInWechat.value)

  // SDK状态检查
  console.log('2. SDK状态:')
  console.log('  - SDK状态:', wechatSDK.getStatus())
  console.log('  - 支持扫码:', supportWxScan.value)
  console.log('  - Store状态:', wechatStore.state)

  // 使用新的权限检查工具
  console.log('3. 扫码权限检查:')
  try {
    const permissionResult = await checkScanPermission()
    console.log('  - 权限检查结果:', permissionResult)

    if (!permissionResult.canScan) {
      console.log('  - 错误信息:', permissionResult.error)
      console.log('  - 修复建议:', permissionResult.suggestions)
    }
  }
  catch (error) {
    console.log('  - 权限检查异常:', error)
  }

  // 微信JS-SDK检查
  const wx = (window as any).wx
  console.log('4. 微信JS-SDK:')
  console.log('  - wx对象存在:', !!wx)
  if (wx) {
    console.log('  - scanQRCode方法存在:', typeof wx.scanQRCode === 'function')
    console.log('  - checkJsApi方法存在:', typeof wx.checkJsApi === 'function')
  }

  console.log('=== 调试信息结束 ===')

  // 显示调试信息弹窗
  showDebugInfo.value = true
}

// 手动修复扫码权限
async function handleFixPermission() {
  if (fixingPermission.value)
    return

  try {
    fixingPermission.value = true
    console.log('开始手动修复扫码权限...')

    const success = await fixScanPermission()

    if (success) {
      showSuccessToast('权限修复成功')
      // 重新检查权限状态
      await debugWechatConfig()
    }
    else {
      showFailToast('权限修复失败')
    }
  }
  catch (error) {
    console.error('手动修复权限失败:', error)
    showFailToast('修复过程中出现错误')
  }
  finally {
    fixingPermission.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 在微信环境中初始化微信SDK
  if (isInWechat.value) {
    initWechat()
  }
})

// 组件销毁时清理资源
onUnmounted(() => {
  showResult.value = false
  showLoginDialog.value = false
  scanning.value = false
  loginLoading.value = false
})
</script>

<template>
  <div class="qr-scanner">
    <!-- 扫码按钮 -->
    <van-button
      :type="buttonType"
      :size="buttonSize"
      :loading="scanning"
      :disabled="disabled || !supportWxScan"
      :class="buttonClass"
      @click="handleScan"
    >
      <template v-if="buttonText" #icon>
        <SvgIcon name="svg/scan" size="20" />
        <!-- <van-icon name="scan" size="24" /> -->
      </template>
      <van-icon v-if="!buttonText" name="scan" size="24" />
      {{ buttonText }}
    </van-button>

    <!-- 调试按钮（仅在开发环境显示） -->
    <van-button
      v-if="isDev"
      type="default"
      size="small"
      class="ml-2"
      @click="debugWechatConfig"
    >
      调试
    </van-button>

    <!-- 扫码结果弹窗 -->
    <van-dialog
      v-model:show="showResult"
      title="扫码结果"
      :show-cancel-button="false"
      confirm-button-text="确定"
      @confirm="handleResultConfirm"
    >
      <div class="result-content p-4">
        <div v-if="scanResult" class="result-info">
          <div class="result-item mb-3">
            <div class="label mb-1 text-sm text-gray-6">
              扫码内容：
            </div>
            <div class="value break-all text-sm">
              {{ scanResult }}
            </div>
          </div>
          <div v-if="parsedResult" class="parsed-result">
            <div class="label mb-1 text-sm text-gray-6">
              解析结果：
            </div>
            <div class="parsed-content text-sm">
              <div v-if="parsedResult.type" class="mb-1">
                <span class="font-medium">类型：</span>{{ parsedResult.type }}
              </div>

              <!-- 工位信息显示 -->
              <template v-if="parsedResult.type === 'workstation_info'">
                <div v-if="parsedResult.workstationName" class="mb-1">
                  <span class="font-medium">工位名称：</span>{{ parsedResult.workstationName }}
                </div>
                <div v-if="parsedResult.levelName" class="mb-1">
                  <span class="font-medium">所属层级：</span>{{ parsedResult.levelName }}
                </div>
                <div v-if="parsedResult.userName" class="mb-1">
                  <span class="font-medium">操作员：</span>{{ parsedResult.userName }}
                </div>
                <div v-if="parsedResult.projectName" class="mb-1">
                  <span class="font-medium">项目名称：</span>{{ parsedResult.projectName }}
                </div>
                <div v-if="parsedResult.countAlgorithm" class="mb-1">
                  <span class="font-medium">计数算法：</span>{{ parsedResult.countAlgorithm }}
                </div>
                <div v-if="parsedResult.source" class="mb-1">
                  <span class="font-medium">数据源：</span>{{ parsedResult.source }}
                </div>
              </template>

              <!-- 登录信息显示 -->
              <template v-if="parsedResult.type === 'login'">
                <div v-if="parsedResult.deviceIp" class="mb-1">
                  <span class="font-medium">设备IP：</span>{{ parsedResult.deviceIp }}
                </div>
                <div v-if="parsedResult.token" class="mb-1">
                  <span class="font-medium">Token：</span>{{ parsedResult.token.substring(0, 8) }}...
                </div>
              </template>
            </div>
          </div>
        </div>
        <div v-else class="no-result py-4 text-center text-gray-5">
          未识别到有效二维码
        </div>
      </div>
    </van-dialog>

    <!-- 登录确认弹窗 -->
    <van-dialog
      v-model:show="showLoginDialog"
      show-cancel-button
      confirm-button-text="确认登录"
      cancel-button-text="取消"
      :confirm-button-loading="loginLoading"
      :cancel-button-disabled="loginLoading"
      @confirm="handleLoginConfirmAction"
      @cancel="closeLoginDialog"
    >
      <div class="login-content p-4">
        <div v-if="loginData" class="login-info">
          <div class="mb-4 text-center">
            <div class="text-lg text-[#333] font-bold">
              确认登录到以下设备？
            </div>
          </div>

          <div class="device-info rounded-lg bg-[#EBFAEC] p-3">
            <div v-if="loginData.deviceIp" class="info-item">
              <span class="label">设备IP：</span>
              <span class="value">{{ loginData.deviceIp }}</span>
            </div>
            <div class="info-item">
              <span class="label">登录时间：</span>
              <span class="value">{{ new Date().toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 调试信息弹窗 -->
    <van-dialog
      v-model:show="showDebugInfo"
      title="微信SDK调试信息"
      :show-cancel-button="false"
      confirm-button-text="关闭"
    >
      <div class="debug-content p-4">
        <div class="debug-info text-sm">
          <div class="mb-2">
            <span class="font-medium">当前环境：</span>{{ isInWechat ? '微信' : '非微信' }}
          </div>
          <div class="mb-2">
            <span class="font-medium">SDK状态：</span>{{ wechatSDK.getStatus() }}
          </div>
          <div class="mb-2">
            <span class="font-medium">支持扫码：</span>{{ supportWxScan ? '是' : '否' }}
          </div>
          <div class="mb-2">
            <span class="font-medium">当前URL：</span>
            <div class="mt-1 break-all text-xs text-gray-6">
              {{ currentUrl }}
            </div>
          </div>

          <!-- 修复按钮 -->
          <div class="mt-4 flex gap-2">
            <van-button
              type="primary"
              size="small"
              :loading="fixingPermission"
              @click="handleFixPermission"
            >
              修复权限
            </van-button>
            <van-button
              type="default"
              size="small"
              @click="debugWechatConfig"
            >
              重新检查
            </van-button>
          </div>

          <div class="mt-3 text-xs text-gray-5">
            请查看控制台获取详细调试信息
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style lang="less" scoped>
.qr-scanner {
  display: inline-block;
}

.result-content {
  .result-info {
    .result-item {
      .label {
        font-weight: 500;
      }

      .value {
        color: #333;
        line-height: 1.4;
      }
    }

    .parsed-result {
      .parsed-content {
        background: #f7f8fa;
        border-radius: 4px;
        padding: 8px;

        > div {
          color: #666;

          .font-medium {
            color: #333;
          }
        }
      }
    }
  }
}

.login-content {
  .device-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: #666;
      }

      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
