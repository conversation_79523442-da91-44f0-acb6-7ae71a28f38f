<template>
  <div class="test-scan-page p-4">
    <van-nav-bar title="扫码功能测试" left-arrow @click-left="$router.back()" />
    
    <div class="mt-4">
      <van-cell-group>
        <van-cell title="环境信息" />
        <van-cell title="当前环境" :value="environmentInfo.current" />
        <van-cell title="User Agent" :value="environmentInfo.userAgent" />
        <van-cell title="微信环境" :value="environmentInfo.isWechat ? '是' : '否'" />
        <van-cell title="小程序环境" :value="environmentInfo.isMiniProgram ? '是' : '否'" />
        <van-cell title="__wxjs_environment" :value="environmentInfo.wxjsEnvironment" />
      </van-cell-group>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="扫码支持情况" />
        <van-cell title="微信 JS-SDK 扫码" :value="scanSupport.wechatScan ? '支持' : '不支持'" />
        <van-cell title="小程序扫码" :value="scanSupport.miniProgramScan ? '支持' : '不支持'" />
        <van-cell title="整体支持" :value="scanSupport.anyScan ? '支持' : '不支持'" />
      </van-cell-group>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="扫码测试" />
      </van-cell-group>
      
      <div class="p-4 text-center">
        <QrScanner
          button-text="测试扫码"
          button-type="primary"
          button-size="large"
          :on-success="handleScanSuccess"
          :on-error="handleScanError"
        />
      </div>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="扫码结果" />
      </van-cell-group>
      
      <div class="p-4">
        <van-field
          v-model="scanResult"
          type="textarea"
          placeholder="扫码结果将显示在这里"
          readonly
          rows="4"
        />
      </div>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="测试工具" />
      </van-cell-group>
      
      <div class="p-4 space-y-2">
        <van-button 
          type="default" 
          block 
          @click="testPostMessage"
        >
          测试 PostMessage 通信
        </van-button>
        
        <van-button 
          type="default" 
          block 
          @click="testEnvironmentDetection"
        >
          重新检测环境
        </van-button>
        
        <van-button 
          type="default" 
          block 
          @click="showLogs"
        >
          显示调试日志
        </van-button>
      </div>
    </div>

    <!-- 日志弹窗 -->
    <van-dialog
      v-model:show="showLogDialog"
      title="调试日志"
      :show-cancel-button="false"
      confirm-button-text="关闭"
    >
      <div class="log-content p-4 max-h-80 overflow-y-auto">
        <pre class="text-xs">{{ logs.join('\n') }}</pre>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { showSuccessToast, showFailToast } from 'vant'
import QrScanner from '@/components/QrScanner/index.vue'
import { 
  isInMiniProgramWebview, 
  canUseMiniProgramScan,
  smartScan 
} from '@/utils/miniProgramScan'
import { isWechatBrowser, wechatSDK } from '@/utils/wechat'
import { useWechatSDK } from '@/store/wechat'

const wechatStore = useWechatSDK()
const scanResult = ref('')
const showLogDialog = ref(false)
const logs = ref<string[]>([])

// 环境信息
const environmentInfo = computed(() => ({
  current: isInMiniProgramWebview() ? '小程序 webview' : (isWechatBrowser() ? '微信' : '其他'),
  userAgent: navigator.userAgent,
  isWechat: isWechatBrowser(),
  isMiniProgram: isInMiniProgramWebview(),
  wxjsEnvironment: (window as any).__wxjs_environment || '未设置'
}))

// 扫码支持情况
const scanSupport = computed(() => ({
  wechatScan: wechatStore.canScan,
  miniProgramScan: canUseMiniProgramScan(),
  anyScan: wechatStore.canScan || canUseMiniProgramScan()
}))

// 添加日志
function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
  console.log(message)
}

// 扫码成功处理
function handleScanSuccess(result: string, parsedResult?: any) {
  scanResult.value = result
  addLog(`扫码成功: ${result}`)
  if (parsedResult) {
    addLog(`解析结果: ${JSON.stringify(parsedResult, null, 2)}`)
  }
  showSuccessToast('扫码成功')
}

// 扫码失败处理
function handleScanError(error: Error) {
  addLog(`扫码失败: ${error.message}`)
  showFailToast(`扫码失败: ${error.message}`)
}

// 测试 PostMessage 通信
function testPostMessage() {
  addLog('开始测试 PostMessage 通信...')
  
  try {
    const wx = (window as any).wx
    if (wx && wx.miniProgram && wx.miniProgram.postMessage) {
      wx.miniProgram.postMessage({
        data: {
          type: 'test',
          message: 'Hello from webview',
          timestamp: Date.now()
        }
      })
      addLog('已通过 wx.miniProgram.postMessage 发送测试消息')
    } else if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        type: 'test',
        message: 'Hello from webview',
        timestamp: Date.now()
      }, '*')
      addLog('已通过 window.parent.postMessage 发送测试消息')
    } else {
      addLog('无法找到可用的 PostMessage 通道')
    }
  } catch (error) {
    addLog(`PostMessage 测试失败: ${error}`)
  }
}

// 重新检测环境
function testEnvironmentDetection() {
  addLog('重新检测环境...')
  addLog(`User Agent: ${navigator.userAgent}`)
  addLog(`是否微信环境: ${isWechatBrowser()}`)
  addLog(`是否小程序环境: ${isInMiniProgramWebview()}`)
  addLog(`__wxjs_environment: ${(window as any).__wxjs_environment}`)
  addLog(`wx 对象存在: ${!!(window as any).wx}`)
  addLog(`微信扫码支持: ${wechatStore.canScan}`)
  addLog(`小程序扫码支持: ${canUseMiniProgramScan()}`)
}

// 显示日志
function showLogs() {
  showLogDialog.value = true
}

// 监听小程序消息
function setupMessageListener() {
  window.addEventListener('message', (event) => {
    addLog(`收到消息: ${JSON.stringify(event.data)}`)
  })
}

onMounted(() => {
  addLog('页面加载完成')
  testEnvironmentDetection()
  setupMessageListener()
})
</script>

<style scoped>
.test-scan-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.log-content pre {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
