/**
 * 微信相关配置
 */

// 微信JS-SDK版本
export const WX_SDK_VERSION = '1.6.0'

// 微信JS-SDK CDN地址
export const WX_SDK_URL = `https://res.wx.qq.com/open/js/jweixin-${WX_SDK_VERSION}.js`

// 默认的JS接口列表
export const DEFAULT_JS_API_LIST = [
  // 基础接口
  'checkJsApi', // 判断当前客户端版本是否支持指定JS接口

  // 微信扫一扫 - 确保扫码权限在最前面
  'scanQRCode', // 调起微信扫一扫接口

  // 分享接口
  'onMenuShareTimeline', // 分享到朋友圈
  'onMenuShareAppMessage', // 分享给朋友
  'onMenuShareQQ', // 分享到QQ
  'onMenuShareWeibo', // 分享到腾讯微博
  'onMenuShareQZone', // 分享到QQ空间

  // 图像接口
  'chooseImage', // 拍照或从手机相册中选图
  'previewImage', // 预览图片
  'uploadImage', // 上传图片
  'downloadImage', // 下载图片
  'getLocalImgData', // 获取本地图片接口

  // 音频接口
  'startRecord', // 开始录音
  'stopRecord', // 停止录音
  'onVoiceRecordEnd', // 监听录音自动停止
  'playVoice', // 播放语音
  'pauseVoice', // 暂停播放
  'stopVoice', // 停止播放
  'onVoicePlayEnd', // 监听语音播放完毕
  'uploadVoice', // 上传语音
  'downloadVoice', // 下载语音

  // 智能接口
  'translateVoice', // 识别音频并返回识别结果

  // 设备信息
  'getNetworkType', // 获取网络状态

  // 地理位置
  'getLocation', // 获取地理位置
  'openLocation', // 使用微信内置地图查看位置

  // 摇一摇周边
  'startSearchBeacons', // 开启查找周边ibeacon设备
  'stopSearchBeacons', // 关闭查找周边ibeacon设备
  'onSearchBeacons', // 监听周边ibeacon设备

  // 界面操作
  'closeWindow', // 关闭当前网页窗口
  'hideMenuItems', // 批量隐藏功能按钮
  'showMenuItems', // 批量显示功能按钮
  'hideAllNonBaseMenuItem', // 隐藏所有非基础按钮
  'showAllNonBaseMenuItem', // 显示所有功能按钮

  // 微信小店
  'openProductSpecificView', // 跳转微信商品页

  // 微信卡券
  'chooseCard', // 拉取适用卡券列表并获取用户选择信息
  'addCard', // 批量添加卡券
  'openCard', // 查看微信卡包中的卡券

  // 微信支付
  'chooseWXPay', // 发起一个微信支付请求

  // 快速输入
  'openAddress', // 共享收货地址
]

// 扫码专用的精简JS接口列表
export const SCAN_JS_API_LIST = [
  'checkJsApi', // 判断当前客户端版本是否支持指定JS接口
  'scanQRCode', // 调起微信扫一扫接口
]

// 扫码类型配置
export const SCAN_TYPES = {
  QR_CODE: 'qrCode', // 二维码
  BAR_CODE: 'barCode', // 一维码
  DATA_MATRIX: 'datamatrix', // Data Matrix码
  PDF417: 'pdf417', // PDF417码
} as const

// 扫码配置预设
export const SCAN_PRESETS = {
  // 默认配置：支持二维码和一维码
  DEFAULT: {
    needResult: 1,
    scanType: [SCAN_TYPES.QR_CODE, SCAN_TYPES.BAR_CODE],
  },
  // 仅二维码
  QR_ONLY: {
    needResult: 1,
    scanType: [SCAN_TYPES.QR_CODE],
  },
  // 仅一维码
  BAR_ONLY: {
    needResult: 1,
    scanType: [SCAN_TYPES.BAR_CODE],
  },
  // 所有类型
  ALL_TYPES: {
    needResult: 1,
    scanType: [
      SCAN_TYPES.QR_CODE,
      SCAN_TYPES.BAR_CODE,
      SCAN_TYPES.DATA_MATRIX,
      SCAN_TYPES.PDF417,
    ],
  },
} as const

// 微信环境检测正则
export const WECHAT_UA_REGEX = /micromessenger/i

// 微信开发者工具检测正则
export const WECHAT_DEVTOOLS_REGEX = /wechatdevtools/i

// 微信小程序检测正则
export const WECHAT_MINIPROGRAM_REGEX = /miniprogram/i

// 错误码映射
export const WX_ERROR_CODES = {
  // 通用错误
  INVALID_URL: 'invalid url domain', // 当前页面所在域名与使用的appid没有绑定
  INVALID_SIGNATURE: 'invalid signature', // 签名错误
  SYSTEM_ERROR: 'system:function_not_exist', // 系统错误
  
  // 扫码相关错误
  SCAN_CANCEL: 'cancel', // 用户取消扫码
  SCAN_FAIL: 'fail', // 扫码失败
  
  // 权限相关错误
  PERMISSION_DENIED: 'permission denied', // 权限拒绝
  FUNCTION_NOT_EXIST: 'function not exist', // 功能不存在
} as const

// 微信配置
export const WECHAT_CONFIG = {
  // 是否开启调试模式（开发环境）
  DEBUG: process.env.NODE_ENV === 'development',
  
  // SDK版本
  SDK_VERSION: WX_SDK_VERSION,
  
  // SDK URL
  SDK_URL: WX_SDK_URL,
  
  // 默认JS接口列表
  DEFAULT_JS_API_LIST,
  
  // 扫码预设
  SCAN_PRESETS,
  
  // 错误码
  ERROR_CODES: WX_ERROR_CODES,
} as const
