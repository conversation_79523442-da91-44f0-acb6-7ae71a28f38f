/**
 * 微信JS-SDK工具类
 * 基于微信官方文档：https://developers.weixin.qq.com/doc/service/guide/h5/jssdk.html
 */
import { WECHAT_CONFIG, WECHAT_UA_REGEX } from '@/config/wechat'

// 微信JS-SDK配置接口
export interface WxConfig {
  debug?: boolean // 开启调试模式
  appId: string // 必填，公众号的唯一标识
  timestamp: number // 必填，生成签名的时间戳
  nonceStr: string // 必填，生成签名的随机串
  signature: string // 必填，签名
  jsApiList: string[] // 必填，需要使用的JS接口列表
}

// 扫码结果接口
export interface ScanResult {
  resultStr: string // 扫码结果
}

// 扫码配置接口
export interface ScanConfig {
  needResult?: 0 | 1 // 默认为0，扫描结果由微信处理，1则直接返回扫描结果
  scanType?: string[] // 可以指定扫二维码还是一维码，默认二者都有
  success?: (res: ScanResult) => void // 接口调用成功的回调函数
  fail?: (error: any) => void // 接口调用失败的回调函数
  complete?: () => void // 接口调用完成的回调函数（调用成功、失败都会执行）
}

// 微信JS-SDK状态
export enum WxSdkStatus {
  NOT_LOADED = 'not_loaded', // 未加载
  LOADING = 'loading', // 加载中
  LOADED = 'loaded', // 已加载
  READY = 'ready', // 已就绪
  ERROR = 'error', // 错误
}

class WechatSDK {
  private status: WxSdkStatus = WxSdkStatus.NOT_LOADED
  private config: WxConfig | null = null
  private readyCallbacks: Array<() => void> = []
  private errorCallbacks: Array<(error: any) => void> = []
  private initPromise: Promise<void> | null = null // 添加初始化Promise，避免重复初始化

  /**
   * 检查是否在微信环境中
   */
  isWechatBrowser(): boolean {
    const ua = navigator.userAgent.toLowerCase()
    return WECHAT_UA_REGEX.test(ua)
  }

  /**
   * 检查是否在小程序 webview 环境中
   */
  isMiniProgramWebview(): boolean {
    const ua = navigator.userAgent.toLowerCase()
    return WECHAT_MINIPROGRAM_REGEX.test(ua) || this.isInMiniProgram()
  }

  /**
   * 检查是否在小程序环境中（通过 window.__wxjs_environment 判断）
   */
  isInMiniProgram(): boolean {
    return typeof window !== 'undefined' && (window as any).__wxjs_environment === 'miniprogram'
  }

  /**
   * 检查微信JS-SDK是否已加载
   */
  isWxLoaded(): boolean {
    return typeof window !== 'undefined' && !!(window as any).wx
  }

  /**
   * 动态加载微信JS-SDK
   */
  loadWxSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isWxLoaded()) {
        this.status = WxSdkStatus.LOADED
        resolve()
        return
      }

      if (this.status === WxSdkStatus.LOADING) {
        // 如果正在加载，等待加载完成
        const checkLoaded = () => {
          if (this.isWxLoaded()) {
            this.status = WxSdkStatus.LOADED
            resolve()
          }
          else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
        return
      }

      this.status = WxSdkStatus.LOADING

      const script = document.createElement('script')
      script.src = WECHAT_CONFIG.SDK_URL
      script.async = true

      script.onload = () => {
        this.status = WxSdkStatus.LOADED
        resolve()
      }

      script.onerror = () => {
        this.status = WxSdkStatus.ERROR
        reject(new Error('微信JS-SDK加载失败'))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 配置微信JS-SDK（带去重逻辑）
   */
  async configWx(config: WxConfig): Promise<void> {
    // 如果已经初始化完成且状态正常，直接返回
    if (this.status === WxSdkStatus.READY) {
      return
    }

    // 如果正在初始化，等待初始化完成
    if (this.initPromise) {
      return this.initPromise
    }

    // 开始新的初始化
    this.initPromise = this._doConfigWx(config)

    try {
      await this.initPromise
    }
    finally {
      // 初始化完成后清除Promise，允许下次重新初始化（如果失败的话）
      if (this.status === WxSdkStatus.ERROR) {
        this.initPromise = null
      }
    }
  }

  /**
   * 实际执行微信JS-SDK配置的内部方法
   */
  private async _doConfigWx(config: WxConfig): Promise<void> {
    try {
      // 确保SDK已加载
      await this.loadWxSDK()

      if (!this.isWxLoaded()) {
        throw new Error('微信JS-SDK未加载')
      }

      this.config = config
      const wx = (window as any).wx

      return new Promise((resolve, reject) => {
        wx.config({
          debug: config.debug || false,
          appId: config.appId,
          timestamp: config.timestamp,
          nonceStr: config.nonceStr,
          signature: config.signature,
          jsApiList: config.jsApiList,
        })

        wx.ready(() => {
          this.status = WxSdkStatus.READY
          this.readyCallbacks.forEach(callback => callback())
          this.readyCallbacks = []
          resolve()
        })

        wx.error((error: any) => {
          this.status = WxSdkStatus.ERROR
          this.errorCallbacks.forEach(callback => callback(error))
          this.errorCallbacks = []
          reject(error)
        })
      })
    }
    catch (error) {
      this.status = WxSdkStatus.ERROR
      throw error
    }
  }

  /**
   * 等待微信JS-SDK就绪
   */
  ready(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.status === WxSdkStatus.READY) {
        resolve()
        return
      }

      if (this.status === WxSdkStatus.ERROR) {
        reject(new Error('微信JS-SDK配置失败'))
        return
      }

      this.readyCallbacks.push(resolve)
      this.errorCallbacks.push(reject)
    })
  }

  /**
   * 调用微信扫码功能
   */
  async scanQRCode(config: ScanConfig = {}): Promise<ScanResult> {
    try {
      await this.ready()

      const wx = (window as any).wx
      if (!wx || !wx.scanQRCode) {
        throw new Error('微信扫码功能不可用')
      }

      return new Promise((resolve, reject) => {
        wx.scanQRCode({
          needResult: config.needResult || 1,
          scanType: config.scanType || ['qrCode', 'barCode'],
          success: (res: ScanResult) => {
            config.success?.(res)
            resolve(res)
          },
          fail: (error: any) => {
            config.fail?.(error)
            reject(error)
          },
          complete: config.complete,
        })
      })
    }
    catch (error) {
      throw new Error(`微信扫码失败: ${error}`)
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): WxSdkStatus {
    return this.status
  }

  /**
   * 重置状态
   */
  reset(): void {
    this.status = WxSdkStatus.NOT_LOADED
    this.config = null
    this.readyCallbacks = []
    this.errorCallbacks = []
    this.initPromise = null
  }
}

// 创建单例实例
export const wechatSDK = new WechatSDK()

// 导出工具函数
export const isWechatBrowser = () => wechatSDK.isWechatBrowser()
export const configWechat = (config: WxConfig) => wechatSDK.configWx(config)
export const scanQRCode = (config?: ScanConfig) => wechatSDK.scanQRCode(config)
export const waitWechatReady = () => wechatSDK.ready()
