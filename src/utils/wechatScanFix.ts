/**
 * 微信扫码权限问题修复工具
 */
import { showFailToast, showSuccessToast } from 'vant'
import { wechatSDK, type WxConfig } from '@/utils/wechat'
import { SCAN_JS_API_LIST, WX_ERROR_CODES } from '@/config/wechat'
import { getWxConfig, getCurrentPageUrl } from '@/components/QrScanner/wechat'

export interface ScanPermissionCheckResult {
  canScan: boolean
  error?: string
  suggestions: string[]
}

/**
 * 检查微信扫码权限
 */
export async function checkScanPermission(): Promise<ScanPermissionCheckResult> {
  const result: ScanPermissionCheckResult = {
    canScan: false,
    suggestions: []
  }

  try {
    // 1. 检查是否在微信环境
    if (!wechatSDK.isWechatBrowser()) {
      result.error = '当前不在微信环境中'
      result.suggestions.push('请在微信中打开此页面')
      return result
    }

    // 2. 检查微信JS-SDK是否加载
    const wx = (window as any).wx
    if (!wx) {
      result.error = '微信JS-SDK未加载'
      result.suggestions.push('请检查网络连接', '刷新页面重试')
      return result
    }

    // 3. 检查扫码API是否存在
    if (typeof wx.scanQRCode !== 'function') {
      result.error = '微信扫码API不可用'
      result.suggestions.push('请更新微信版本', '检查微信JS-SDK版本')
      return result
    }

    // 4. 检查API权限
    if (typeof wx.checkJsApi === 'function') {
      const apiCheck = await new Promise<any>((resolve) => {
        wx.checkJsApi({
          jsApiList: ['scanQRCode'],
          success: resolve,
          fail: resolve
        })
      })

      console.log('API权限检查结果:', apiCheck)

      if (apiCheck.checkResult && apiCheck.checkResult.scanQRCode === false) {
        result.error = 'scanQRCode API权限不可用'
        result.suggestions.push(
          '检查微信公众号后台JS接口安全域名配置',
          '确认当前域名已添加到安全域名列表',
          '检查微信签名是否正确'
        )
        return result
      }
    }

    // 5. 尝试调用扫码API（不实际扫码，只检查权限）
    try {
      // 这里不会真正打开扫码界面，只是检查权限
      await new Promise((resolve, reject) => {
        // 设置一个很短的超时，如果没有权限错误，说明权限正常
        const timeout = setTimeout(() => {
          resolve('permission_ok')
        }, 100)

        wx.scanQRCode({
          needResult: 1,
          scanType: ['qrCode'],
          success: () => {
            clearTimeout(timeout)
            resolve('success')
          },
          fail: (error: any) => {
            clearTimeout(timeout)
            reject(error)
          }
        })
      })

      result.canScan = true
      return result
    } catch (scanError: any) {
      console.error('扫码权限测试失败:', scanError)
      
      if (scanError.errMsg) {
        if (scanError.errMsg.includes('permission denied')) {
          result.error = '扫码权限被拒绝'
          result.suggestions.push(
            '检查微信公众号后台配置',
            '确认JS接口安全域名设置正确',
            '检查微信签名配置'
          )
        } else if (scanError.errMsg.includes('invalid signature')) {
          result.error = '微信签名验证失败'
          result.suggestions.push(
            '联系管理员检查微信签名配置',
            '确认服务器时间同步正确'
          )
        } else if (scanError.errMsg.includes('invalid url domain')) {
          result.error = '当前域名未配置为安全域名'
          result.suggestions.push(
            '在微信公众号后台添加当前域名到JS接口安全域名',
            '确认域名格式正确（不包含协议和端口）'
          )
        } else {
          result.error = `扫码权限检查失败: ${scanError.errMsg}`
          result.suggestions.push('请联系技术支持')
        }
      } else {
        result.error = '扫码权限检查失败'
        result.suggestions.push('请联系技术支持')
      }

      return result
    }
  } catch (error) {
    console.error('权限检查异常:', error)
    result.error = `权限检查异常: ${error}`
    result.suggestions.push('请刷新页面重试', '联系技术支持')
    return result
  }
}

/**
 * 尝试修复微信扫码权限问题
 */
export async function fixScanPermission(): Promise<boolean> {
  try {
    console.log('开始修复微信扫码权限...')

    // 1. 重置微信SDK状态
    wechatSDK.reset()

    // 2. 重新获取微信配置（使用扫码专用配置）
    const url = getCurrentPageUrl()
    const configData = await getWxConfig({ url })

    // 3. 使用扫码专用的JS接口列表重新配置
    const scanConfig: WxConfig = {
      debug: false, // 生产环境关闭调试
      appId: configData.appId,
      timestamp: Number.parseInt(configData.timestamp) || Date.now(),
      nonceStr: configData.nonceStr,
      signature: configData.signature,
      jsApiList: SCAN_JS_API_LIST // 使用精简的扫码专用接口列表
    }

    console.log('使用扫码专用配置重新初始化:', scanConfig)

    // 4. 重新配置微信SDK
    await wechatSDK.configWx(scanConfig)

    // 5. 等待SDK就绪
    await wechatSDK.ready()

    // 6. 验证修复结果
    const checkResult = await checkScanPermission()
    
    if (checkResult.canScan) {
      showSuccessToast('扫码权限修复成功')
      return true
    } else {
      showFailToast(`修复失败: ${checkResult.error}`)
      return false
    }
  } catch (error) {
    console.error('修复扫码权限失败:', error)
    showFailToast('修复失败，请联系技术支持')
    return false
  }
}

/**
 * 显示权限检查结果和修复建议
 */
export function showPermissionSuggestions(result: ScanPermissionCheckResult) {
  if (result.canScan) {
    showSuccessToast('扫码权限正常')
    return
  }

  const message = [
    `错误: ${result.error}`,
    '',
    '建议解决方案:',
    ...result.suggestions.map((s, i) => `${i + 1}. ${s}`)
  ].join('\n')

  console.error('扫码权限问题:', message)
  showFailToast(result.error || '扫码权限检查失败')
}

/**
 * 获取常见错误的解决方案
 */
export function getErrorSolution(errorMessage: string): string[] {
  const solutions: string[] = []

  if (errorMessage.includes('permission denied')) {
    solutions.push(
      '检查微信公众号后台"JS接口安全域名"配置',
      '确认当前域名已正确添加到安全域名列表',
      '检查域名格式（不包含http://和端口号）'
    )
  }

  if (errorMessage.includes('invalid signature')) {
    solutions.push(
      '检查微信签名配置是否正确',
      '确认服务器时间与微信服务器时间同步',
      '检查access_token是否有效'
    )
  }

  if (errorMessage.includes('invalid url domain')) {
    solutions.push(
      '在微信公众号后台添加当前域名',
      '确认域名配置格式正确',
      '等待域名配置生效（可能需要几分钟）'
    )
  }

  if (solutions.length === 0) {
    solutions.push(
      '刷新页面重试',
      '检查网络连接',
      '联系技术支持'
    )
  }

  return solutions
}
