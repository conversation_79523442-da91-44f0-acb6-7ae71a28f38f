# 小程序 Webview 扫码功能配置指南

## 概述

当网页内嵌到小程序的 webview 中时，微信 JS-SDK 的扫码功能可能受到限制。为了在小程序 webview 环境中实现扫码功能，需要通过小程序的 postMessage 机制与小程序通信。

## 环境检测

我们的 QrScanner 组件会自动检测当前环境：

- **普通微信环境**: 使用微信 JS-SDK 扫码
- **小程序 webview 环境**: 使用智能扫码（postMessage + 小程序扫码 API）

## 小程序端配置

### 1. 小程序页面配置

在小程序的页面 `.js` 文件中添加以下代码：

```javascript
Page({
  data: {
    webviewUrl: 'https://your-domain.com/your-page'
  },

  onLoad() {
    // 监听来自 webview 的消息
    this.bindWebviewMessage()
  },

  // 绑定 webview 消息监听
  bindWebviewMessage() {
    // 监听 webview 发送的消息
    wx.onMessage && wx.onMessage((data) => {
      console.log('收到 webview 消息:', data)
      
      if (data.type === 'requestScan') {
        this.handleScanRequest()
      }
    })
  },

  // 处理扫码请求
  handleScanRequest() {
    console.log('开始扫码...')
    
    wx.scanCode({
      success: (res) => {
        console.log('扫码成功:', res)
        this.sendMessageToWebview({
          type: 'scanResult',
          success: true,
          result: res.result
        })
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        this.sendMessageToWebview({
          type: 'scanResult',
          success: false,
          error: error.errMsg || '扫码失败'
        })
      }
    })
  },

  // 发送消息到 webview
  sendMessageToWebview(data) {
    const webview = this.selectComponent('#webview')
    if (webview) {
      webview.postMessage({ data })
    } else {
      console.error('未找到 webview 组件')
    }
  },

  // webview 消息处理（备用方法）
  onWebviewMessage(event) {
    console.log('webview 消息事件:', event.detail.data)
    // 这里可以处理其他类型的消息
  }
})
```

### 2. 小程序页面模板配置

在小程序的页面 `.wxml` 文件中：

```xml
<web-view 
  id="webview" 
  src="{{webviewUrl}}" 
  bindmessage="onWebviewMessage">
</web-view>
```

### 3. 小程序权限配置

确保小程序具有扫码权限，在 `app.json` 中添加：

```json
{
  "permission": {
    "scope.camera": {
      "desc": "用于扫码功能"
    }
  }
}
```

## Webview 端使用

### 基本使用

```vue
<template>
  <QrScanner
    button-text="扫一扫"
    :on-success="handleScanSuccess"
    :on-error="handleScanError"
  />
</template>

<script setup>
import QrScanner from '@/components/QrScanner/index.vue'

function handleScanSuccess(result, parsedResult) {
  console.log('扫码成功:', result, parsedResult)
}

function handleScanError(error) {
  console.error('扫码失败:', error)
}
</script>
```

### 环境检测

```typescript
import { isInMiniProgramWebview, canUseMiniProgramScan } from '@/utils/miniProgramScan'

// 检测是否在小程序环境
if (isInMiniProgramWebview()) {
  console.log('当前在小程序 webview 环境中')
  
  if (canUseMiniProgramScan()) {
    console.log('支持小程序扫码')
  } else {
    console.log('小程序扫码功能不可用，需要配置')
  }
}
```

## 调试和故障排除

### 1. 使用调试工具

在开发环境中，扫码按钮旁边会显示"调试"按钮，点击可以查看：

- 当前环境类型（微信/小程序 webview）
- 各种扫码方式的支持状态
- 详细的错误信息

### 2. 常见问题

#### 问题1：扫码按钮被禁用
**原因**: 检测到不支持扫码的环境
**解决**: 
- 检查是否在微信或小程序环境中
- 确认小程序端已正确配置扫码功能

#### 问题2：扫码无响应
**原因**: 小程序端未正确监听 webview 消息
**解决**:
- 检查小程序页面是否正确实现了 `wx.onMessage` 监听
- 确认 webview 组件 ID 正确

#### 问题3：扫码超时
**原因**: 小程序端处理时间过长或未响应
**解决**:
- 检查小程序端的扫码逻辑
- 确认消息发送机制正常

### 3. 控制台日志

查看浏览器控制台，关键日志包括：

```
开始扫码...
环境检测: { isInWechat: true, isInMiniProgram: true, ... }
检测到小程序环境，使用智能扫码...
开始智能扫码...
尝试通过 postMessage 扫码...
已发送扫码请求到小程序
收到小程序消息: { type: 'scanResult', success: true, result: '...' }
扫码结果: ...
```

## 测试验证

### 1. 环境测试

```javascript
// 在浏览器控制台中执行
console.log('User Agent:', navigator.userAgent)
console.log('__wxjs_environment:', window.__wxjs_environment)
console.log('wx 对象:', window.wx)
```

### 2. 消息通信测试

```javascript
// 测试向小程序发送消息
if (window.wx && window.wx.miniProgram) {
  window.wx.miniProgram.postMessage({
    data: { type: 'test', message: 'Hello from webview' }
  })
}
```

## 最佳实践

1. **错误处理**: 始终提供友好的错误提示和降级方案
2. **超时设置**: 设置合理的扫码超时时间（建议30秒）
3. **用户引导**: 在扫码功能不可用时，提供清晰的使用说明
4. **日志记录**: 记录详细的调试信息，便于问题排查

## 更新日志

### v1.2.0
- 添加小程序 webview 环境检测
- 实现智能扫码功能
- 支持 postMessage 通信机制
- 添加小程序扫码配置指南

### v1.1.0
- 添加自动权限检查和修复功能
- 增强错误处理和用户提示
- 添加调试工具和详细日志
