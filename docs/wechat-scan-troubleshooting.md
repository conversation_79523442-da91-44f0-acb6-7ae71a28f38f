# 微信扫码权限问题排查指南

## 问题描述

当使用微信扫码功能时，可能会遇到 `scanQRCode:permission denied` 错误。这通常是由于微信JS-SDK配置问题导致的。

## 常见错误类型

### 1. permission denied
**错误信息**: `scanQRCode:permission denied`
**原因**: 扫码权限被拒绝
**解决方案**:
- 检查微信公众号后台"JS接口安全域名"配置
- 确认当前域名已正确添加到安全域名列表
- 检查域名格式（不包含http://和端口号）

### 2. invalid signature
**错误信息**: `invalid signature`
**原因**: 微信签名验证失败
**解决方案**:
- 检查微信签名配置是否正确
- 确认服务器时间与微信服务器时间同步
- 检查access_token是否有效

### 3. invalid url domain
**错误信息**: `invalid url domain`
**原因**: 当前域名未配置为微信安全域名
**解决方案**:
- 在微信公众号后台添加当前域名
- 确认域名配置格式正确
- 等待域名配置生效（可能需要几分钟）

## 自动修复功能

我们的QrScanner组件现在包含了自动权限检查和修复功能：

### 权限检查
- 自动检测微信环境
- 验证JS-SDK加载状态
- 检查扫码API权限
- 提供详细的错误信息和修复建议

### 自动修复
- 重置微信SDK状态
- 使用精简的扫码专用JS接口列表重新配置
- 自动重试权限验证

### 调试工具
在开发环境中，扫码按钮旁边会显示"调试"按钮，点击可以：
- 查看详细的SDK状态信息
- 手动触发权限修复
- 重新检查权限状态

## 使用方法

### 基本使用
```vue
<template>
  <QrScanner
    button-text="扫一扫"
    :on-success="handleScanSuccess"
    :on-error="handleScanError"
  />
</template>

<script setup>
import QrScanner from '@/components/QrScanner/index.vue'

function handleScanSuccess(result, parsedResult) {
  console.log('扫码成功:', result, parsedResult)
}

function handleScanError(error) {
  console.error('扫码失败:', error)
}
</script>
```

### 手动权限检查
```typescript
import { checkScanPermission, fixScanPermission } from '@/utils/wechatScanFix'

// 检查权限
const result = await checkScanPermission()
if (!result.canScan) {
  console.log('权限问题:', result.error)
  console.log('修复建议:', result.suggestions)
  
  // 尝试修复
  const fixed = await fixScanPermission()
  if (fixed) {
    console.log('权限修复成功')
  }
}
```

## 微信公众号配置

### JS接口安全域名设置
1. 登录微信公众平台
2. 进入"设置与开发" -> "公众号设置" -> "功能设置"
3. 在"JS接口安全域名"中添加您的域名
4. 注意：域名格式为 `example.com`，不包含 `http://` 和端口号

### 必需的JS接口权限
确保以下接口已启用：
- `checkJsApi` - 检查JS接口支持
- `scanQRCode` - 扫码功能

## 故障排除步骤

1. **检查环境**
   - 确认在微信客户端中打开
   - 检查网络连接

2. **检查配置**
   - 验证域名配置
   - 检查签名生成

3. **使用调试工具**
   - 点击调试按钮查看详细信息
   - 尝试手动修复权限

4. **查看控制台**
   - 检查详细的错误日志
   - 查看权限检查结果

## 技术支持

如果问题仍然存在，请：
1. 收集控制台错误日志
2. 记录当前URL和环境信息
3. 联系技术支持团队

## 更新日志

### v1.1.0
- 添加自动权限检查和修复功能
- 增强错误处理和用户提示
- 添加调试工具和详细日志

### v1.0.0
- 基础扫码功能
- 支持二维码和条形码
- 结果解析和处理
